// out: false
// <PERSON><PERSON> Styling
#cmplz-cookiebanner-container {
  position: fixed !important;
  bottom: @vw30 !important;
  right: @vw30 !important;
  z-index: 9999 !important;
  max-width: @vw100 * 2 !important;
  
  .cmplz-cookiebanner {
    background: @secondaryColor !important;
    border: none !important;
    .rounded(@vw12);
    padding: @vw30 !important;
    box-shadow: 0 @vw10 @vw40 rgba(0, 0, 0, 0.2) !important;
    color: @hardWhite !important;
    font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
    
    // Header styling
    .cmplz-header {
      margin-bottom: @vw20 !important;
      
      .cmplz-logo {
        display: none !important;
      }
      
      .cmplz-title {
        .smallTitle;
        color: @hardWhite !important;
        margin: 0 !important;
        font-size: @vw24 !important;
        line-height: 1.2 !important;
      }
      
      .cmplz-close {
        position: absolute !important;
        top: @vw15 !important;
        right: @vw15 !important;
        width: @vw24 !important;
        height: @vw24 !important;
        cursor: pointer !important;
        .transition(.3s);
        
        svg {
          width: @vw16 !important;
          height: @vw16 !important;
          fill: @hardWhite !important;
        }
        
        &:hover {
          opacity: 0.7 !important;
        }
      }
    }
    
    // Divider
    .cmplz-divider {
      display: none !important;
    }
    
    // Body content
    .cmplz-body {
      .cmplz-message {
        color: @hardWhite !important;
        font-size: @vw16 !important;
        line-height: 1.4 !important;
        margin-bottom: @vw20 !important;
        opacity: 0.9 !important;
        
        p {
          color: @hardWhite !important;
          margin: 0 !important;
          font-size: @vw16 !important;
          line-height: 1.4 !important;
        }
      }
      
      // Hide categories section
      .cmplz-categories {
        display: none !important;
      }
    }
    
    // Links section - hide manage options etc
    .cmplz-links.cmplz-information {
      display: none !important;
    }
    
    // Footer divider
    .cmplz-divider.cmplz-footer {
      display: none !important;
    }
    
    // Buttons styling
    .cmplz-buttons {
      display: flex !important;
      flex-direction: column !important;
      gap: @vw12 !important;
      margin-top: @vw20 !important;
      
      .cmplz-btn {
        .rounded(@vw6);
        border: none !important;
        padding: @vw12 @vw20 !important;
        font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
        font-size: @vw16 !important;
        font-weight: normal !important;
        cursor: pointer !important;
        .transition(.3s);
        text-align: center !important;
        width: 100% !important;
        
        &.cmplz-accept {
          background: @hardWhite !important;
          color: @secondaryColor !important;
          
          &:hover {
            background: rgba(255, 255, 255, 0.9) !important;
            .transform(translateY(-1px));
          }
        }
        
        &.cmplz-deny {
          background: transparent !important;
          color: @hardWhite !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          
          &:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
          }
        }
        
        // Hide other buttons
        &.cmplz-view-preferences,
        &.cmplz-save-preferences,
        &.cmplz-manage-options {
          display: none !important;
        }
      }
    }
    
    // Documents links - hide privacy statement
    .cmplz-links.cmplz-documents {
      display: none !important;
    }
  }
}

// Responsive styling
@media all and (max-width: 1080px) {
  #cmplz-cookiebanner-container {
    bottom: @vw30-1080 !important;
    right: @vw30-1080 !important;
    max-width: @vw400-1080 !important;
    
    .cmplz-cookiebanner {
      .rounded(@vw12-1080);
      padding: @vw30-1080 !important;
      
      .cmplz-header {
        margin-bottom: @vw20-1080 !important;
        
        .cmplz-title {
          font-size: @vw24-1080 !important;
        }
        
        .cmplz-close {
          top: @vw15-1080 !important;
          right: @vw15-1080 !important;
          width: @vw24-1080 !important;
          height: @vw24-1080 !important;
          
          svg {
            width: @vw16-1080 !important;
            height: @vw16-1080 !important;
          }
        }
      }
      
      .cmplz-body {
        .cmplz-message {
          font-size: @vw16-1080 !important;
          margin-bottom: @vw20-1080 !important;
          
          p {
            font-size: @vw16-1080 !important;
          }
        }
      }
      
      .cmplz-buttons {
        gap: @vw12-1080 !important;
        margin-top: @vw20-1080 !important;
        
        .cmplz-btn {
          .rounded(@vw6-1080);
          padding: @vw12-1080 @vw20-1080 !important;
          font-size: @vw16-1080 !important;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  #cmplz-cookiebanner-container {
    bottom: @vw20-580 !important;
    right: @vw20-580 !important;
    left: @vw20-580 !important;
    max-width: none !important;
    
    .cmplz-cookiebanner {
      .rounded(@vw12-580);
      padding: @vw25-580 !important;
      
      .cmplz-header {
        margin-bottom: @vw20-580 !important;
        
        .cmplz-title {
          font-size: @vw28-580 !important;
          padding-right: @vw30-580 !important;
        }
        
        .cmplz-close {
          top: @vw15-580 !important;
          right: @vw15-580 !important;
          width: @vw28-580 !important;
          height: @vw28-580 !important;
          
          svg {
            width: @vw18-580 !important;
            height: @vw18-580 !important;
          }
        }
      }
      
      .cmplz-body {
        .cmplz-message {
          font-size: @vw18-580 !important;
          margin-bottom: @vw20-580 !important;
          
          p {
            font-size: @vw18-580 !important;
          }
        }
      }
      
      .cmplz-buttons {
        gap: @vw12-580 !important;
        margin-top: @vw20-580 !important;
        
        .cmplz-btn {
          .rounded(@vw8-580);
          padding: @vw15-580 @vw20-580 !important;
          font-size: @vw18-580 !important;
        }
      }
    }
  }
}
