/* <PERSON><PERSON> Banner Styling */
#cmplz-cookiebanner-container {
  position: fixed !important;
  bottom: 1.736vw !important;
  right: 1.736vw !important;
  z-index: 9999 !important;
  max-width: 23.148vw !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner {
  background: #5A51A3 !important;
  border: none !important;
  border-radius: 0.694vw !important;
  padding: 1.736vw !important;
  box-shadow: 0 0.579vw 2.315vw rgba(0, 0, 0, 0.2) !important;
  color: #FFFFFF !important;
  font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
}

/* Header styling */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header {
  margin-bottom: 1.157vw !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-logo {
  display: none !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-title {
  font-size: 1.389vw !important;
  letter-spacing: 0 !important;
  font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
  font-weight: normal !important;
  font-style: normal !important;
  line-height: 1.15 !important;
  color: #FFFFFF !important;
  margin: 0 !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close {
  position: absolute !important;
  top: 0.868vw !important;
  right: 0.868vw !important;
  width: 1.389vw !important;
  height: 1.389vw !important;
  cursor: pointer !important;
  transition: opacity 0.3s ease-out !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close svg {
  width: 0.926vw !important;
  height: 0.926vw !important;
  fill: #FFFFFF !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close:hover {
  opacity: 0.7 !important;
}

/* Divider */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-divider {
  display: none !important;
}

/* Body content */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message {
  color: #FFFFFF !important;
  font-size: 0.926vw !important;
  line-height: 1.4 !important;
  margin-bottom: 1.157vw !important;
  opacity: 0.9 !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message p {
  color: #FFFFFF !important;
  margin: 0 !important;
  font-size: 0.926vw !important;
  line-height: 1.4 !important;
}

/* Hide categories section */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-categories {
  display: none !important;
}

/* Links section - hide manage options etc */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-links.cmplz-information {
  display: none !important;
}

/* Footer divider */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-divider.cmplz-footer {
  display: none !important;
}

/* Buttons styling */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.694vw !important;
  margin-top: 1.157vw !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn {
  border-radius: 0.347vw !important;
  border: none !important;
  padding: 0.694vw 1.157vw !important;
  font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
  font-size: 0.926vw !important;
  font-weight: normal !important;
  cursor: pointer !important;
  transition: all 0.3s ease-out !important;
  text-align: center !important;
  width: 100% !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-accept {
  background: #FFFFFF !important;
  color: #5A51A3 !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-accept:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  transform: translateY(-1px) !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-deny {
  background: transparent !important;
  color: #FFFFFF !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-deny:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* Hide other buttons */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-view-preferences,
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-save-preferences,
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn.cmplz-manage-options {
  display: none !important;
}

/* Documents links - hide privacy statement */
#cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-links.cmplz-documents {
  display: none !important;
}

/* Responsive styling for 1080px */
@media all and (max-width: 1080px) {
  #cmplz-cookiebanner-container {
    bottom: 2.778vw !important;
    right: 2.778vw !important;
    max-width: 37.037vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner {
    border-radius: 1.111vw !important;
    padding: 2.778vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header {
    margin-bottom: 1.852vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-title {
    font-size: 2.222vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close {
    top: 1.389vw !important;
    right: 1.389vw !important;
    width: 2.222vw !important;
    height: 2.222vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close svg {
    width: 1.481vw !important;
    height: 1.481vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message {
    font-size: 1.481vw !important;
    margin-bottom: 1.852vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message p {
    font-size: 1.481vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons {
    gap: 1.111vw !important;
    margin-top: 1.852vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn {
    border-radius: 0.556vw !important;
    padding: 1.111vw 1.852vw !important;
    font-size: 1.481vw !important;
  }
}

/* Responsive styling for 580px */
@media all and (max-width: 580px) {
  #cmplz-cookiebanner-container {
    bottom: 3.448vw !important;
    right: 3.448vw !important;
    left: 3.448vw !important;
    max-width: none !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner {
    border-radius: 2.069vw !important;
    padding: 4.310vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header {
    margin-bottom: 3.448vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-title {
    font-size: 4.827vw !important;
    padding-right: 5.172vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close {
    top: 2.586vw !important;
    right: 2.586vw !important;
    width: 4.827vw !important;
    height: 4.827vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-header .cmplz-close svg {
    width: 3.103vw !important;
    height: 3.103vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message {
    font-size: 3.103vw !important;
    margin-bottom: 3.448vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-body .cmplz-message p {
    font-size: 3.103vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons {
    gap: 2.069vw !important;
    margin-top: 3.448vw !important;
  }
  
  #cmplz-cookiebanner-container .cmplz-cookiebanner .cmplz-buttons .cmplz-btn {
    border-radius: 1.379vw !important;
    padding: 2.586vw 3.448vw !important;
    font-size: 3.103vw !important;
  }
}
